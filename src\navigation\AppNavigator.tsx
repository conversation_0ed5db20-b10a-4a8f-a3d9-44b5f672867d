import React, { useState, useEffect, useRef } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';
import { Product } from '../types';

// Import screens
import CanvasScreen from '../screens/CanvasScreen';
import ProductsScreen from '../screens/ProductsScreen';
import SettingsScreen from '../screens/SettingsScreen';
import ExportScreen from '../screens/ExportScreen';
import HelpScreen from '../screens/HelpScreen';

// Import components
import CustomDrawerContent from '../components/CustomDrawerContent';

const AppNavigator: React.FC = () => {
  const { isDark } = useTheme();
  const { state, saveCanvas, updateCurrentCanvas } = useData();
  const [currentScreen, setCurrentScreen] = useState('Canvas');
  const [showMenu, setShowMenu] = useState(false);


  const handleProductSelect = (product: Product) => {
    // Handle product selection from drawer
    console.log('Product selected from drawer:', product.name);
  };

  const handleProductAddToCanvas = async (product: Product, position: { x: number; y: number }) => {
    try {
      const canvasId: string = state.currentCanvas?.id || 'default';
      const newCanvasProduct = {
        ...product,
        position,
        canvasId,
      };

      const updatedProducts = [...(state.currentCanvas?.products || []), newCanvasProduct];
      const updatedCanvas = {
        id: canvasId,
        name: state.currentCanvas?.name || 'My Canvas',
        products: updatedProducts,
        connections: state.currentCanvas?.connections || [],
      };

      await saveCanvas(updatedCanvas);
      updateCurrentCanvas({
        ...updatedCanvas,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      alert(`Success: ${product.name} added to canvas`);
    } catch (error) {
      alert('Error: Failed to add product to canvas');
      console.error('Error adding product:', error);
    }
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    height: '100vh',
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
  };

  const drawerStyle: React.CSSProperties = {
    width: '280px',
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
    borderRight: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    display: 'flex',
    flexDirection: 'column',
  };

  const mainContentStyle: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  };

  const headerStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
    borderBottom: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    padding: '12px 16px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  const headerTitleStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: 'bold',
    color: isDark ? '#ffffff' : '#000000',
    margin: 0,
  };

  const menuButtonStyle: React.CSSProperties = {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    fontSize: '20px',
    color: isDark ? '#ffffff' : '#000000',
  };

  const threeDotsButtonStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#374151' : '#e5e7eb',
    border: 'none',
    cursor: 'pointer',
    fontSize: '24px',
    color: isDark ? '#ffffff' : '#000000',
    padding: '4px 8px',
    borderRadius: '4px',
    transition: 'background-color 0.2s',
    fontFamily: 'Arial, sans-serif',
  };

  const dropdownStyle: React.CSSProperties = {
    position: 'absolute',
    top: '100%',
    right: 0,
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
    border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    zIndex: 1000,
    minWidth: '150px',
  };

  const menuItemStyle: React.CSSProperties = {
    padding: '12px 16px',
    cursor: 'pointer',
    color: isDark ? '#ffffff' : '#000000',
    borderBottom: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
  };

  const menuItemLastStyle: React.CSSProperties = {
    ...menuItemStyle,
    borderBottom: 'none',
  };


  const screenContainerStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'auto',
  };

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'Canvas':
        return <CanvasScreen onNavigate={setCurrentScreen} />;
      case 'Products':
        return <ProductsScreen onNavigate={setCurrentScreen} onProductSelect={(product) => handleProductAddToCanvas(product, { x: 0, y: 0 })} />;
      case 'Settings':
        return <SettingsScreen onNavigate={setCurrentScreen} />;
      case 'Export':
        return <ExportScreen onNavigate={setCurrentScreen} />;
      case 'Help':
        return <HelpScreen onNavigate={setCurrentScreen} />;
      default:
        return <CanvasScreen onNavigate={setCurrentScreen} />;
    }
  };

  return (
    <div style={containerStyle}>
      {/* Custom Drawer */}
      <div style={drawerStyle}>
        <CustomDrawerContent
          showProductsSidebar={currentScreen === 'Canvas'}
          onProductSelect={handleProductAddToCanvas}
          onNavigate={setCurrentScreen}
        />
      </div>

      {/* Main Content */}
      <div style={mainContentStyle}>
        {/* Header */}
        <div style={headerStyle}>
          <button
            style={menuButtonStyle}
            onClick={() => {
              // Toggle drawer visibility could be implemented here
              console.log('Menu button pressed');
            }}
          >
            ☰
          </button>
          <h1 style={{...headerTitleStyle, flex: 1}}>
            {currentScreen === 'Canvas' && (state.currentCanvas?.name || 'Canvas')}
            {currentScreen === 'Products' && 'Products'}
            {currentScreen === 'Settings' && 'Settings'}
            {currentScreen === 'Export' && 'Export'}
            {currentScreen === 'Help' && 'Help & Support'}
          </h1>
          <div style={{position: 'relative'}}>
            <button
              style={threeDotsButtonStyle}
              onClick={() => setShowMenu(!showMenu)}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = isDark ? '#374151' : '#f3f4f6';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              ...
            </button>
            {showMenu && (
              <div style={dropdownStyle}>
                <div style={menuItemStyle} onClick={() => { setCurrentScreen('Canvas'); setShowMenu(false); }}>🎨 Canvas</div>
                <div style={menuItemStyle} onClick={() => { setCurrentScreen('Products'); setShowMenu(false); }}>📦 Products</div>
                <div style={menuItemStyle} onClick={() => { setCurrentScreen('Settings'); setShowMenu(false); }}>⚙️ Settings</div>
                <div style={menuItemStyle} onClick={() => { setCurrentScreen('Export'); setShowMenu(false); }}>💾 Export</div>
                <div style={menuItemLastStyle} onClick={() => { setCurrentScreen('Help'); setShowMenu(false); }}>❓ Help & Support</div>
              </div>
            )}
          </div>
        </div>

        {/* Screen Content */}
        <div style={screenContainerStyle}>
          {renderCurrentScreen()}
        </div>
      </div>
    </div>
  );
};

export default AppNavigator;