<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>PlomDesign Mobile - Web Preview</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="theme-color" content="#3b82f6">
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .container {
            width: 100%;
            max-width: 420px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
        }

        .phone-mockup {
            position: relative;
            background: #000;
            border-radius: 30px;
            padding: 15px;
            margin: 0 auto 20px;
        }

        .phone-screen {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }

        #root {
            width: 100%;
            min-height: 700px;
            position: relative;
        }

        .instructions {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .instructions h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
            font-size: 18px;
        }

        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #4b5563;
            font-size: 14px;
        }

        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100px;
            color: #6b7280;
            font-size: 14px;
        }

        /* Mobile-specific styles */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                max-width: 100%;
            }

            .phone-mockup {
                padding: 10px;
            }
        }

        /* Hide scrollbars for mobile feel */
        #root::-webkit-scrollbar {
            display: none;
        }

        #root {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body>
    <div class="status" id="status">Loading...</div>

    <div class="container">
        <div class="phone-mockup">
            <div class="phone-screen">
                <div id="root">
                    <div class="loading">
                        🚀 Loading PlomDesign Mobile...
                    </div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>🎮 How to Test Your App:</h3>
            <ul>
                <li><strong>Navigation:</strong> Click the ☰ menu button to open drawer</li>
                <li><strong>Canvas:</strong> Go to Canvas screen for drag & drop demo</li>
                <li><strong>Touch:</strong> Use mouse to simulate touch gestures</li>
                <li><strong>Theme:</strong> Toggle between dark/light mode</li>
                <li><strong>Responsive:</strong> Resize browser to test different screen sizes</li>
            </ul>
        </div>
    </div>

    <script>
        // Mock React Native modules for web compatibility
        window.ReactNative = window.ReactNative || {};
        window.ReactNative.Platform = { OS: 'web', select: (obj) => obj.web || obj.default };
        window.ReactNative.Dimensions = {
            get: () => ({
                window: { width: window.innerWidth, height: window.innerHeight },
                screen: { width: window.screen.width, height: window.screen.height }
            }),
            addEventListener: () => ({ remove: () => {} })
        };

        // Mock SQLite for web (will use localStorage)
        window.sqlitePlugin = {
            openDatabase: () => ({
                transaction: (callback) => callback({
                    executeSql: (query, params, success, error) => {
                        // Simple mock - in real app, use proper web database
                        console.log('SQL Query:', query, params);
                        success && success(null, { rows: { raw: () => [], length: 0 } });
                    }
                })
            })
        };

        // Update status when loaded
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('status').textContent = '✅ App Loaded!';
                document.getElementById('status').style.background = '#10b981';
            }, 2000);
        });

        // Handle window resize for responsive testing
        window.addEventListener('resize', () => {
            // Force re-render on resize - removed infinite loop
            // window.dispatchEvent(new Event('resize'));
        });
    </script>

    <script src="/index.bundle?platform=web&dev=true&minify=false"></script>
</body>
</html>