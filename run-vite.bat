@echo off
echo ====================================
echo   PlomDesignMobile - Vite Version
echo ====================================
echo.

echo Switching to Vite configuration...
if exist package.json (
    move package.json package.rn.json >nul 2>&1
)

if exist package.vite.json (
    move package.vite.json package.json >nul 2>&1
    echo ✓ Switched to Vite version
) else (
    echo ✗ Error: package.vite.json not found!
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
call npm install --legacy-peer-deps

if %errorlevel% neq 0 (
    echo ✗ Failed to install dependencies
    echo.
    echo Trying with force flag...
    call npm install --force
    if %errorlevel% neq 0 (
        echo ✗ Still failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo ✓ Dependencies installed successfully
echo.
echo Starting Vite development server...
echo ====================================
echo.
echo 🌐 App will be available at: http://localhost:3000
echo 📱 Open in browser to test the app
echo.
echo Press Ctrl+C to stop the server
echo ====================================
echo.

npm run dev