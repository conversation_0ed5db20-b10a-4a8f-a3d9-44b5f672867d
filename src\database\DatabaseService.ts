import { Product, Canvas, CanvasProduct, Connection, Settings, User, LicenseInfo } from '../types';

// Web storage interface for TypeScript
interface WebStorage {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

declare const localStorage: WebStorage;

class DatabaseService {
  private db: any = null;
  private readonly DB_KEY = 'plomdesign_sqlite_db';

  async init(): Promise<void> {
    try {
      // Load sql.js script dynamically
      await this.loadSqlJsScript();

      // Access initSqlJs from global scope
      const initSqlJs = (globalThis as any).initSqlJs;

      if (typeof initSqlJs !== 'function') {
        throw new Error('initSqlJs is not available in global scope');
      }

      // Configure sql.js to find the WASM file
      const SQL = await initSqlJs({
        locateFile: (file: string) => {
          // In Vite, the WASM file is served from the public directory
          if (file === 'sql-wasm.wasm') {
            return '/sql-wasm.wasm';
          }
          return file;
        }
      });

      const dbData = localStorage.getItem(this.DB_KEY);
      if (dbData) {
        const dbArray = Uint8Array.from(atob(dbData), c => c.charCodeAt(0));
        this.db = new SQL.Database(dbArray);
      } else {
        this.db = new SQL.Database();
        this.createTables();
      }
      console.log('SQLite database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private loadSqlJsScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if sql.js is already loaded
      if ((globalThis as any).initSqlJs) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = '/node_modules/sql.js/dist/sql-wasm.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load sql.js script'));
      document.head.appendChild(script);
    });
  }

  private createTables(): void {
    if (!this.db) return;

    // Check if products table exists and get its schema
    const tableInfo = this.db.exec("PRAGMA table_info(products)");
    const existingColumns = tableInfo.length > 0 ? tableInfo[0].values.map((row: any) => row[1]) : [];

    if (existingColumns.length === 0) {
      // Table doesn't exist, create it with all columns
      this.db.run(`
        CREATE TABLE products (
          id TEXT PRIMARY KEY,
          name TEXT,
          category TEXT,
          subcategory TEXT,
          image TEXT,
          description TEXT,
          quantity INTEGER,
          price REAL,
          material TEXT,
          positionX REAL,
          positionY REAL,
          rotation REAL,
          createdAt TEXT,
          updatedAt TEXT
        )
      `);
    } else {
      // Table exists, add missing columns
      if (!existingColumns.includes('price')) {
        try {
          this.db.run(`ALTER TABLE products ADD COLUMN price REAL`);
          console.log('Added price column to existing products table');
        } catch (e) {
          console.warn('Failed to add price column:', e);
        }
      }

      if (!existingColumns.includes('material')) {
        try {
          this.db.run(`ALTER TABLE products ADD COLUMN material TEXT`);
          console.log('Added material column to existing products table');
        } catch (e) {
          console.warn('Failed to add material column:', e);
        }
      }
    }

    this.db.run(`
      CREATE TABLE IF NOT EXISTS canvases (
        id TEXT PRIMARY KEY,
        name TEXT,
        createdAt TEXT,
        updatedAt TEXT
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS canvas_products (
        id TEXT PRIMARY KEY,
        canvasId TEXT,
        productId TEXT,
        x REAL,
        y REAL,
        createdAt TEXT,
        updatedAt TEXT,
        FOREIGN KEY (canvasId) REFERENCES canvases (id),
        FOREIGN KEY (productId) REFERENCES products (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS connections (
        id TEXT PRIMARY KEY,
        canvasId TEXT,
        fromId TEXT,
        toId TEXT,
        createdAt TEXT,
        FOREIGN KEY (canvasId) REFERENCES canvases (id)
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY,
        theme TEXT,
        autoSave INTEGER,
        autoSaveInterval INTEGER,
        visibleCategories TEXT,
        removedCategories TEXT,
        deletedCategories TEXT,
        removedSubcategories TEXT,
        deletedSubcategories TEXT,
        removedProducts TEXT,
        deletedProducts TEXT,
        createdAt TEXT,
        updatedAt TEXT
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT,
        email TEXT,
        isLoggedIn INTEGER,
        createdAt TEXT,
        updatedAt TEXT
      )
    `);

    this.db.run(`
      CREATE TABLE IF NOT EXISTS license_info (
        id TEXT PRIMARY KEY,
        licenseKey TEXT,
        isValid INTEGER,
        expiryDate TEXT,
        features TEXT,
        maxProducts INTEGER,
        createdAt TEXT,
        updatedAt TEXT
      )
    `);
  }

  private saveDatabase(): void {
    if (!this.db) return;
    try {
      const data = this.db.export();
      // Convert Uint8Array to base64 in chunks to avoid stack overflow
      let binary = '';
      const chunkSize = 8192; // Process in 8KB chunks
      for (let i = 0; i < data.length; i += chunkSize) {
        const chunk = data.slice(i, i + chunkSize);
        binary += String.fromCharCode.apply(null, Array.from(chunk));
      }
      const base64 = btoa(binary);
      localStorage.setItem(this.DB_KEY, base64);
    } catch (error) {
      console.error('Error saving database to localStorage:', error);
      throw new Error(`Failed to save database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Product operations
  async getAllProducts(): Promise<Product[]> {
    if (!this.db) throw new Error('Database not initialized');
    const result = this.db.exec('SELECT * FROM products ORDER BY createdAt DESC');
    if (result.length === 0) return [];
    const products: Product[] = result[0].values.map((row: any) => ({
      id: row[0],
      name: row[1],
      category: row[2],
      subcategory: row[3],
      image: row[4],
      description: row[5],
      quantity: row[6],
      price: row[7],
      material: row[8],
      position: { x: row[9], y: row[10] },
      rotation: row[11],
      createdAt: row[12],
      updatedAt: row[13],
    }));
    return products;
  }

  async saveProduct(product: Omit<Product, 'createdAt' | 'updatedAt'>, isRetry: boolean = false): Promise<void> {
    console.log('DatabaseService.saveProduct called with:', product, 'isRetry:', isRetry);

    if (!this.db) {
      console.error('Database not initialized when trying to save product');
      throw new Error('Database not initialized');
    }

    // Validate required fields
    if (!product.id) {
      console.error('Product ID is missing');
      throw new Error('Product ID is required');
    }
    if (!product.name || !product.name.trim()) {
      console.error('Product name is missing or empty');
      throw new Error('Product name is required');
    }
    if (!product.category || !product.category.trim()) {
      console.error('Product category is missing or empty');
      throw new Error('Product category is required');
    }
    if (!product.position) {
      console.error('Product position is missing');
      throw new Error('Product position is required');
    }

    const now = new Date().toISOString();
    console.log('Preparing SQL statement for product save');

    try {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO products (id, name, category, subcategory, image, description, quantity, price, material, positionX, positionY, rotation, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const params = [
        product.id,
        product.name,
        product.category,
        product.subcategory || '',
        product.image || null,
        product.description || null,
        product.quantity || 0,
        product.price || null,
        product.material || null,
        product.position.x || 0,
        product.position.y || 0,
        product.rotation || 0,
        now,
        now
      ];

      console.log('Executing SQL with parameters:', params);
      stmt.run(params);
      stmt.free();

      console.log('Product saved successfully, now saving database to localStorage');
      this.saveDatabase();
      console.log('Database saved to localStorage successfully');
    } catch (error) {
      console.error('Error during SQL execution:', error);

      // Check if it's a schema-related error and we haven't already retried
      if (error instanceof Error && error.message.includes('no column named') && !isRetry) {
        console.log('Schema error detected, attempting to recreate database...');
        try {
          await this.recreateDatabase();
          // Retry the save operation once
          return this.saveProduct(product, true);
        } catch (recreateError) {
          console.error('Failed to recreate database:', recreateError);
          throw new Error(`Failed to save product due to schema error. Database recreation also failed: ${recreateError instanceof Error ? recreateError.message : 'Unknown error'}`);
        }
      }

      throw new Error(`Failed to save product to database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deleteProduct(productId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    this.db.run('DELETE FROM products WHERE id = ?', [productId]);
    this.saveDatabase();
  }

  // Canvas operations
  async getAllCanvases(): Promise<Canvas[]> {
    if (!this.db) throw new Error('Database not initialized');

    const canvasesResult = this.db.exec('SELECT * FROM canvases ORDER BY createdAt DESC');
    if (canvasesResult.length === 0) return [];

    const canvases: Canvas[] = [];
    for (const canvasRow of canvasesResult[0].values) {
      const canvasId = canvasRow[0] as string;

      // Get products for this canvas
      const productsResult = this.db.exec(`SELECT * FROM canvas_products WHERE canvasId = '${canvasId}'`);
      const products: CanvasProduct[] = productsResult.length > 0 ? productsResult[0].values.map((row: any) => ({
        id: row[0] as string,
        canvasId: row[1] as string,
        productId: row[2] as string,
        x: row[3] as number,
        y: row[4] as number,
        createdAt: row[5] as string,
        updatedAt: row[6] as string,
        // Note: This is simplified, you might need to join with products table for full product data
        name: '', category: '', subcategory: '', quantity: 0, position: {x: row[3] as number, y: row[4] as number}, rotation: 0
      })) : [];

      // Get connections for this canvas
      const connectionsResult = this.db.exec(`SELECT * FROM connections WHERE canvasId = '${canvasId}'`);
      const connections: Connection[] = connectionsResult.length > 0 ? connectionsResult[0].values.map((row: any) => ({
        id: row[0] as string,
        canvasId: row[1] as string,
        from: row[2] as string,
        to: row[3] as string,
        createdAt: row[4] as string,
      })) : [];

      canvases.push({
        id: canvasRow[0] as string,
        name: canvasRow[1] as string,
        createdAt: canvasRow[2] as string,
        updatedAt: canvasRow[3] as string,
        products,
        connections,
      });
    }

    return canvases;
  }

  async saveCanvas(canvas: Omit<Canvas, 'createdAt' | 'updatedAt'>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    const now = new Date().toISOString();

    // Save canvas
    const canvasStmt = this.db.prepare(`
      INSERT OR REPLACE INTO canvases (id, name, createdAt, updatedAt)
      VALUES (?, ?, ?, ?)
    `);
    canvasStmt.run([canvas.id, canvas.name, now, now]);
    canvasStmt.free();

    // Remove existing products and connections
    const deleteProductsStmt = this.db.prepare('DELETE FROM canvas_products WHERE canvasId = ?');
    deleteProductsStmt.run([canvas.id]);
    deleteProductsStmt.free();

    const deleteConnectionsStmt = this.db.prepare('DELETE FROM connections WHERE canvasId = ?');
    deleteConnectionsStmt.run([canvas.id]);
    deleteConnectionsStmt.free();

    // Add new products
    for (const product of canvas.products) {
      // Generate unique ID for canvas-product relationship
      const canvasProductId = `${canvas.id}_${product.id}`;

      this.db.run(`
        INSERT INTO canvas_products (id, canvasId, productId, x, y, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        canvasProductId,
        canvas.id,
        product.id,
        product.position.x,
        product.position.y,
        now,
        now
      ]);
    }

    // Add new connections
    const connectionStmt = this.db.prepare(`
      INSERT INTO connections (id, canvasId, fromId, toId, createdAt)
      VALUES (?, ?, ?, ?, ?)
    `);
    for (const connection of canvas.connections) {
      connectionStmt.run([
        connection.id,
        canvas.id,
        connection.from,
        connection.to,
        now
      ]);
    }
    connectionStmt.free();

    this.saveDatabase();
  }

  // Settings operations
  async getSettings(): Promise<Settings | null> {
    if (!this.db) throw new Error('Database not initialized');
    const result = this.db.exec('SELECT * FROM settings LIMIT 1');
    if (result.length === 0 || result[0].values.length === 0) return null;
    const row = result[0].values[0];
    return {
      id: row[0] as string,
      theme: row[1] as 'light' | 'dark',
      autoSave: !!row[2],
      autoSaveInterval: row[3] as number,
      visibleCategories: JSON.parse(row[4] as string || '{}'),
      removedCategories: JSON.parse(row[5] as string || '[]'),
      deletedCategories: JSON.parse(row[6] as string || '[]'),
      removedSubcategories: JSON.parse(row[7] as string || '[]'),
      deletedSubcategories: JSON.parse(row[8] as string || '[]'),
      removedProducts: JSON.parse(row[9] as string || '[]'),
      deletedProducts: JSON.parse(row[10] as string || '[]'),
      createdAt: row[11] as string,
      updatedAt: row[12] as string,
    };
  }

  async saveSettings(settings: Omit<Settings, 'createdAt' | 'updatedAt'>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    const now = new Date().toISOString();

    this.db.run(`
      INSERT OR REPLACE INTO settings (id, theme, autoSave, autoSaveInterval, visibleCategories, removedCategories, deletedCategories, removedSubcategories, deletedSubcategories, removedProducts, deletedProducts, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      settings.id,
      settings.theme,
      settings.autoSave ? 1 : 0,
      settings.autoSaveInterval,
      JSON.stringify(settings.visibleCategories),
      JSON.stringify(settings.removedCategories),
      JSON.stringify(settings.deletedCategories),
      JSON.stringify(settings.removedSubcategories),
      JSON.stringify(settings.deletedSubcategories),
      JSON.stringify(settings.removedProducts),
      JSON.stringify(settings.deletedProducts),
      now,
      now
    ]);

    this.saveDatabase();
  }

  // User operations
  async getCurrentUser(): Promise<User | null> {
    if (!this.db) throw new Error('Database not initialized');
    const result = this.db.exec('SELECT * FROM users WHERE isLoggedIn = 1 LIMIT 1');
    if (result.length === 0 || result[0].values.length === 0) return null;
    const row = result[0].values[0];
    return {
      id: row[0] as string,
      username: row[1] as string,
      email: row[2] as string || undefined,
      isLoggedIn: !!row[3],
      createdAt: row[4] as string,
      updatedAt: row[5] as string,
    };
  }

  async saveUser(user: Omit<User, 'createdAt' | 'updatedAt'>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    const now = new Date().toISOString();

    this.db.run(`
      INSERT OR REPLACE INTO users (id, username, email, isLoggedIn, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      user.id,
      user.username,
      user.email || null,
      user.isLoggedIn ? 1 : 0,
      now,
      now
    ]);

    this.saveDatabase();
  }

  // License operations
  async getLicenseInfo(userId: string): Promise<LicenseInfo | null> {
    if (!this.db) throw new Error('Database not initialized');
    const result = this.db.exec(`SELECT * FROM license_info WHERE id = '${userId}' LIMIT 1`);
    if (result.length === 0 || result[0].values.length === 0) return null;
    const row = result[0].values[0];
    return {
      id: row[0] as string,
      licenseKey: row[1] as string,
      isValid: !!row[2],
      expiryDate: row[3] as string || undefined,
      features: JSON.parse(row[4] as string || '[]'),
      maxProducts: row[5] as number,
      createdAt: row[6] as string,
      updatedAt: row[7] as string,
    };
  }

  async saveLicenseInfo(licenseInfo: Omit<LicenseInfo, 'createdAt' | 'updatedAt'>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    const now = new Date().toISOString();

    this.db.run(`
      INSERT OR REPLACE INTO license_info (id, licenseKey, isValid, expiryDate, features, maxProducts, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      licenseInfo.id,
      licenseInfo.licenseKey,
      licenseInfo.isValid ? 1 : 0,
      licenseInfo.expiryDate || null,
      JSON.stringify(licenseInfo.features),
      licenseInfo.maxProducts,
      now,
      now
    ]);

    this.saveDatabase();
  }

  // Utility methods
  async clearAllData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    const tables = ['products', 'canvases', 'canvas_products', 'connections', 'settings', 'users', 'license_info'];
    tables.forEach(table => {
      this.db!.run(`DELETE FROM ${table}`);
    });
    this.saveDatabase();
  }

  async recreateDatabase(): Promise<void> {
    console.log('Recreating database with updated schema...');

    // Clear localStorage to force fresh database creation
    localStorage.removeItem(this.DB_KEY);

    // Reinitialize database
    await this.init();

    console.log('Database recreated successfully');
  }

  async close(): Promise<void> {
    this.saveDatabase();
    console.log('SQLite database session ended');
  }

  // Export database as Uint8Array
  exportDatabase(): Uint8Array | null {
    if (!this.db) return null;
    return this.db.export();
  }

  // Import database from Uint8Array
  async importDatabase(data: Uint8Array): Promise<void> {
    await this.loadSqlJsScript();
    const initSqlJs = (globalThis as any).initSqlJs;
    const SQL = await initSqlJs({
      locateFile: (file: string) => {
        if (file === 'sql-wasm.wasm') {
          return '/sql-wasm.wasm';
        }
        return file;
      }
    });
    this.db = new SQL.Database(data);
    this.saveDatabase();
  }
}

export const databaseService = new DatabaseService();