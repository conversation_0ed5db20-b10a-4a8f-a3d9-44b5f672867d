const express = require('express');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const PORT = 3000;

// Serve static files from web directory
app.use(express.static(path.join(__dirname, 'web')));// Proxy Metro bundler requests
app.use('/index.bundle', createProxyMiddleware({
  target: 'http://localhost:8082',
  changeOrigin: true,
  ws: true,
}));// Serve the main HTML file for all routes (except API routes)
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'web', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`
🌐 PlomDesign Mobile Web Server Started!
📱 Open your browser and go to: http://localhost:${PORT}
🚀 Metro bundler should be running on: http://localhost:8081

📋 Instructions:
1. Make sure Metro is running: npm run web
2. Open http://localhost:3000 in your browser
3. Test the navigation drawer and drag & drop features!

🎮 Features to test:
- Navigation drawer (swipe or click hamburger menu)
- Canvas screen with drag & drop
- Theme switching (dark/light mode)
- Responsive design (resize browser window)
  `);
});