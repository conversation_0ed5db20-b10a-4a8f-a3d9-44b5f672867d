import React, { useEffect, useRef } from 'react';
import { useData } from '../contexts/DataContext';
import { useTheme } from '../contexts/ThemeContext';
import { Product, CanvasProduct } from '../types';

const InvoicePdfScreen: React.FC = () => {
  const { state } = useData();

  // Use only products added to the current canvas
  const invoiceProducts: CanvasProduct[] = state.currentCanvas?.products || [];
  const { isDark } = useTheme();
  const fadeAnim = useRef(0);

  useEffect(() => {
    // Animation placeholder
  }, []);

  const handleGeneratePdf = () => {
    alert('PDF generated successfully!');
  };

  const handleShare = () => {
    const invoiceText = invoiceProducts.map(
      (p, i) =>
        `${i + 1}. ${p.name} (Qty: ${p.quantity})\nCategory: ${p.category} / ${p.subcategory}\nPrice: ${p.price !== undefined && p.price !== null && p.price !== '' ? p.price : '-'}\nMaterial: ${p.material ? p.material : 'N/A'}`
    ).join('\n\n');
    alert(`Invoice Products:\n\n${invoiceText}`);
  };

  return (
    <div style={{ padding: 32, maxWidth: 700, margin: '0 auto' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 24 }}>
        <h2 style={{ fontSize: 28, fontWeight: 700, color: isDark ? '#fff' : '#2c3e50', margin: 0 }}>Invoice PDF</h2>
        <span style={{ fontSize: 32 }}>🧾</span>
      </div>
      <div style={{ background: '#fff', borderRadius: 16, padding: 24, boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
        <h3 style={{ fontSize: 20, fontWeight: 600, color: '#2c3e50', marginBottom: 16 }}>Products</h3>
        {invoiceProducts.length === 0 ? (
          <p style={{ color: '#888', fontSize: 16, textAlign: 'center', margin: '24px 0' }}>No products on the canvas.</p>
        ) : (
          invoiceProducts.map((product: CanvasProduct) => (
            <div key={product.id} style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 18, background: '#f6f8fa', borderRadius: 12, padding: 12 }}>
              {product.image ? (
                <img src={product.image} alt={product.name} style={{ width: 64, height: 64, borderRadius: 8, marginRight: 16, objectFit: 'cover', background: '#eee' }} />
              ) : (
                <div style={{ width: 64, height: 64, borderRadius: 8, background: '#eee', alignItems: 'center', justifyContent: 'center', display: 'flex', marginRight: 16 }}>
                  <span style={{ color: '#aaa' }}>No Image</span>
                </div>
              )}
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: 18, fontWeight: 600, color: '#2c3e50', marginBottom: 4 }}>{product.name}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Category: {product.category} / {product.subcategory}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Quantity: {product.quantity}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Price: {product.price !== undefined && product.price !== null && product.price !== '' ? product.price : '-'}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Material: {product.material ? product.material : 'N/A'}</div>
              </div>
            </div>
          ))
        )}
        <div style={{ height: 32 }} />
        <button style={{ width: '100%', height: 50, background: '#667eea', borderRadius: 12, color: '#fff', fontSize: 17, fontWeight: 600, border: 'none', marginBottom: 14 }} onClick={handleGeneratePdf}>
          Generate PDF
        </button>
        <button style={{ width: '100%', height: 50, border: '2px solid #667eea', borderRadius: 12, color: '#667eea', fontSize: 17, fontWeight: 600, background: 'none' }} onClick={handleShare}>
          Share Invoice
        </button>
      </div>
    </div>
  );
};

export default InvoicePdfScreen;
