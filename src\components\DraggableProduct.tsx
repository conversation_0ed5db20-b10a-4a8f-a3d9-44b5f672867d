import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, Alert } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
  useDerivedValue,
} from 'react-native-reanimated';
import { Product } from '../types';
import { useTheme } from '../contexts/ThemeContext';

interface DraggableProductProps {
  product: Product;
  onDragStart?: (product: Product) => void;
  onDragEnd?: (product: Product, position: { x: number; y: number }) => void;
  onPositionChange?: (productId: string, position: { x: number; y: number }) => void;
  containerWidth: number;
  containerHeight: number;
}

const DraggableProduct: React.FC<DraggableProductProps> = ({
  product,
  onDragStart,
  onDragEnd,
  onPositionChange,
  containerWidth,
  containerHeight,
}) => {
  const { isDark } = useTheme();
  const [isDragging, setIsDragging] = useState(false);

  // Animated values for smooth dragging
  const translateX = useSharedValue(product.position.x);
  const translateY = useSharedValue(product.position.y);
  const scale = useSharedValue(1);

  // Create pan gesture using modern Gesture API
  const panGesture = Gesture.Pan()
    .onStart(() => {
      // Scale up when dragging starts
      scale.value = withSpring(1.1);

      // Notify parent that drag started
      if (onDragStart) {
        runOnJS(onDragStart)(product);
      }

      runOnJS(setIsDragging)(true);
    })
    .onUpdate((event) => {
      // Update position based on gesture
      const newX = product.position.x + event.translationX;
      const newY = product.position.y + event.translationY;

      // Constrain to container bounds
      const constrainedX = Math.max(0, Math.min(newX, containerWidth - 100)); // 100 is approximate product width
      const constrainedY = Math.max(0, Math.min(newY, containerHeight - 100)); // 100 is approximate product height

      translateX.value = constrainedX;
      translateY.value = constrainedY;

      // Notify parent of position change
      if (onPositionChange) {
        runOnJS(onPositionChange)(product.id, { x: constrainedX, y: constrainedY });
      }
    })
    .onEnd(() => {
      // Scale back to normal
      scale.value = withSpring(1);

      // Calculate final position
      const finalX = Math.max(0, Math.min(translateX.value, containerWidth - 100));
      const finalY = Math.max(0, Math.min(translateY.value, containerHeight - 100));

      // Notify parent that drag ended
      if (onDragEnd) {
        runOnJS(onDragEnd)(product, { x: finalX, y: finalY });
      }

      runOnJS(setIsDragging)(false);
    })
    .onFinalize(() => {
      // Scale back to normal
      scale.value = withSpring(1);
      runOnJS(setIsDragging)(false);
    });

  // Animated style for smooth transformations
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
    zIndex: isDragging ? 1000 : 1,
  }));

  const handleLongPress = () => {
    // Provide haptic feedback or visual feedback
    console.log('Long press detected on product:', product.name);
  };

  return (
    <GestureDetector gesture={panGesture}>
      <Animated.View style={[styles.container, animatedStyle]}>
        <View
          style={[
            styles.productCard,
            {
              backgroundColor: isDark ? '#374151' : '#ffffff',
              borderColor: isDragging ? '#3b82f6' : (isDark ? '#4b5563' : '#d1d5db'),
              shadowOpacity: isDragging ? 0.3 : 0.1,
            },
          ]}
        >
          {/* Product Image */}
          {product.image ? (
            <Image
              source={{ uri: product.image }}
              style={styles.productImage}
              resizeMode="contain"
            />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: isDark ? '#4b5563' : '#f3f4f6' }]}>
              <Text style={[styles.placeholderText, { color: isDark ? '#9ca3af' : '#6b7280' }]}>
                {product.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}

          {/* Product Info */}
          <View style={styles.productInfo}>
            <Text
              style={[styles.productName, { color: isDark ? '#ffffff' : '#000000' }]}
              numberOfLines={1}
            >
              {product.name}
            </Text>
            <Text
              style={[styles.productCategory, { color: isDark ? '#9ca3af' : '#6b7280' }]}
              numberOfLines={1}
            >
              {product.category} • {product.subcategory}
            </Text>
          </View>

          {/* Drag Handle Indicator */}
          {isDragging && (
            <View style={styles.dragIndicator}>
              <Text style={styles.dragIndicatorText}>⋮⋮</Text>
            </View>
          )}
        </View>
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  productCard: {
    width: 100,
    height: 100,
    borderRadius: 12,
    borderWidth: 2,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    width: '100%',
    height: 50,
    borderRadius: 6,
    marginBottom: 4,
  },
  placeholderImage: {
    width: '100%',
    height: 50,
    borderRadius: 6,
    marginBottom: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  productCategory: {
    fontSize: 10,
  },
  dragIndicator: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(59, 130, 246, 0.9)',
    borderRadius: 10,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  dragIndicatorText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default DraggableProduct;