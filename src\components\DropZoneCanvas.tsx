import React, { useState, useRef, useCallback } from 'react';
import { View, StyleSheet, Dimensions, Alert } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import DraggableProduct from './DraggableProduct';
import { Product, CanvasProduct } from '../types';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface DropZoneCanvasProps {
  canvasProducts: CanvasProduct[];
  onProductAdd: (product: Product, position: { x: number; y: number }) => void;
  onProductMove: (productId: string, position: { x: number; y: number }) => void;
  onProductRemove: (productId: string) => void;
  style?: any;
}

const DropZoneCanvas: React.FC<DropZoneCanvasProps> = ({
  canvasProducts,
  onProductAdd,
  onProductMove,
  onProductRemove,
  style,
}) => {
  const { isDark } = useTheme();
  const { state } = useData();
  const [isDropZoneActive, setIsDropZoneActive] = useState(false);
  const canvasRef = useRef<View>(null);

  // Shared values for animations
  const dropZoneOpacity = useSharedValue(0.1);
  const dropZoneScale = useSharedValue(1);

  // Animated style for drop zone feedback
  const dropZoneAnimatedStyle = useAnimatedStyle(() => ({
    opacity: dropZoneOpacity.value,
    transform: [{ scale: dropZoneScale.value }],
  }));

  // Handle drag start - show drop zone feedback
  const handleDragStart = useCallback((product: Product) => {
    setIsDropZoneActive(true);
    dropZoneOpacity.value = withSpring(0.2);
    dropZoneScale.value = withSpring(1.02);
  }, [dropZoneOpacity, dropZoneScale]);

  // Handle drag end - hide drop zone feedback and add product
  const handleDragEnd = useCallback((product: Product, position: { x: number; y: number }) => {
    setIsDropZoneActive(false);
    dropZoneOpacity.value = withSpring(0.1);
    dropZoneScale.value = withSpring(1);

    // Check if product is already on canvas
    const existingProduct = canvasProducts.find(cp => cp.id === product.id);
    if (existingProduct) {
      // Update existing product position
      onProductMove(product.id, position);
    } else {
      // Add new product to canvas
      onProductAdd(product, position);
    }
  }, [canvasProducts, onProductAdd, onProductMove, dropZoneOpacity, dropZoneScale]);

  // Handle position changes during drag
  const handlePositionChange = useCallback((productId: string, position: { x: number; y: number }) => {
    // Real-time position updates can be handled here if needed
    // For performance, we might throttle these updates
  }, []);

  // Handle canvas tap to deselect products
  const handleCanvasTap = useCallback(() => {
    // Could implement product selection/deselection logic here
    console.log('Canvas tapped');
  }, []);

  // Create tap gesture for canvas
  const tapGesture = Gesture.Tap()
    .onStart(() => {
      handleCanvasTap();
    });


  return (
    <GestureDetector gesture={tapGesture}>
      <View style={[styles.container, style]}>

        {/* Drop Zone Overlay */}
        <Animated.View
          ref={canvasRef}
          style={[
            styles.dropZone,
            dropZoneAnimatedStyle,
            {
              backgroundColor: isDropZoneActive
                ? (isDark ? 'rgba(96, 165, 250, 0.1)' : 'rgba(59, 130, 246, 0.05)')
                : 'transparent',
              borderColor: isDropZoneActive
                ? '#60a5fa'
                : (isDark ? 'rgba(241, 245, 249, 0.2)' : 'rgba(0,0,0,0.1)'),
            },
          ]}
        >
          {isDropZoneActive && (
            <View style={styles.dropIndicator}>
              <Animated.Text
                style={[
                  styles.dropText,
                  {
                    color: isDark ? '#60a5fa' : '#3b82f6',
                    transform: [{ scale: dropZoneScale }],
                  },
                ]}
              >
                Drop here to add product
              </Animated.Text>
            </View>
          )}
        </Animated.View>

        {/* Canvas Products */}
        {canvasProducts.map((canvasProduct) => (
          <DraggableProduct
            key={canvasProduct.id}
            product={{
              ...canvasProduct,
              position: canvasProduct.position,
            }}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onPositionChange={handlePositionChange}
            containerWidth={screenWidth}
            containerHeight={screenHeight}
          />
        ))}

        {/* Empty State */}
        {canvasProducts.length === 0 && !isDropZoneActive && (
          <View style={styles.emptyState}>
            <Animated.Text
              style={[
                styles.emptyStateText,
                { color: isDark ? '#94a3b8' : '#6b7280' },
              ]}
            >
              Tap and drag products here from the sidebar
            </Animated.Text>
          </View>
        )}
      </View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    backgroundColor: 'transparent',
  },
  dropZone: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropIndicator: {
    alignItems: 'center',
  },
  dropText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
});

export default DropZoneCanvas;