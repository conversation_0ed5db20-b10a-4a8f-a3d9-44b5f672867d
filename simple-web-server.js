const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

const server = http.createServer((req, res) => {
  if (req.url === '/' || req.url === '/index.html') {
    // Serve the HTML file
    const htmlPath = path.join(__dirname, 'web', 'index.html');
    fs.readFile(htmlPath, (err, data) => {
      if (err) {
        res.writeHead(404);
        res.end('File not found');
        return;
      }
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(data);
    });
  } else if (req.url.startsWith('/index.bundle')) {
    // Proxy to Metro bundler with web platform
    const http = require('http');
    const url = new URL(req.url, `http://${req.headers.host}`);
    url.searchParams.set('platform', 'web');

    const options = {
      hostname: 'localhost',
      port: 8082,
      path: url.pathname + url.search,
      method: req.method,
      headers: {
        ...req.headers,
        'platform': 'web'
      }
    };

    const proxyReq = http.request(options, (proxyRes) => {
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      proxyRes.pipe(res);
    });

    proxyReq.on('error', (err) => {
      console.log('Proxy error:', err.message);
      res.writeHead(500);
      res.end('Bundle not available - Make sure Metro is running on port 8082');
    });

    req.pipe(proxyReq);
  } else {
    res.writeHead(404);
    res.end('Not found');
  }
});

server.listen(PORT, () => {
  console.log(`
🌐 PlomDesign Mobile Web Server Started!
📱 Open in MobileView: http://localhost:${PORT}

📋 Next steps:
1. Start Metro bundler: npx metro start --port 8082
2. Open VS Code MobileView extension
3. Enter URL: http://localhost:${PORT}

Status: Ready for testing!
  `);
});