import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';
import { databaseService } from '../database/DatabaseService';

interface ExportScreenProps {
  onNavigate?: (screen: string) => void;
}

const ExportScreen: React.FC<ExportScreenProps> = ({ onNavigate }) => {
  const { isDark } = useTheme();
  const { state, importDatabase } = useData();
  const [isExporting, setIsExporting] = useState(false);


  const exportToSQLite = async () => {
    try {
      const dbData = (databaseService as any).exportDatabase();
      if (!dbData) {
        alert('Error: No database to export');
        return;
      }

      const timestamp = new Date().toISOString().split('T')[0];

      // Web implementation - download file
      const blob = new Blob([dbData], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `plomdesign_backup_${timestamp}.db`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      alert('Success: Database exported successfully');
    } catch (error) {
      alert('Error: Failed to export database');
    }
  };

  const importFromSQLite = async (file: File) => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const dbData = new Uint8Array(arrayBuffer);
      await importDatabase(dbData);
      alert('Success: Database imported successfully. The app has been refreshed automatically.');
    } catch (error) {
      alert('Error: Failed to import database');
    }
  };

  const handleExportSQLite = async () => {
    setIsExporting(true);
    await exportToSQLite();
    setIsExporting(false);
  };

  const handleImportSQLite = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsExporting(true);
      await importFromSQLite(file);
      setIsExporting(false);
    }
  };

  const handleExportCanvasImage = async (canvasId: string) => {
    const canvas = state.canvases.find(c => c.id === canvasId);
    if (!canvas) {
      alert('Error: Canvas not found');
      return;
    }

    alert('Canvas Image Export: Image export functionality would capture the canvas as an image. This feature requires additional implementation.');
  };

  const containerStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: isDark ? '#1e293b' : '#f9fafb',
    padding: '20px',
  };

  const cardStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#ffffff' : '#1e293b',
    borderRadius: '12px',
    padding: '24px',
    marginBottom: '20px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    marginBottom: '16px',
    color: isDark ? '#1e293b' : '#ffffff',
  };

  const sectionTitleStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: '600',
    marginBottom: '12px',
    color: isDark ? '#1e293b' : '#ffffff',
  };

  const exportButtonStyle: React.CSSProperties = {
    backgroundColor: '#3b82f6',
    color: '#ffffff',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '12px',
    width: '100%',
    maxWidth: '300px',
  };

  const secondaryButtonStyle: React.CSSProperties = {
    backgroundColor: '#6b7280',
    color: '#ffffff',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '12px',
    width: '100%',
    maxWidth: '300px',
  };

  const disabledButtonStyle: React.CSSProperties = {
    backgroundColor: '#9ca3af',
    color: '#ffffff',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '8px',
    cursor: 'not-allowed',
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '12px',
    width: '100%',
    maxWidth: '300px',
    opacity: 0.5,
  };

  const statsStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#f9fafb' : '#374151',
    padding: '16px',
    borderRadius: '8px',
    marginBottom: '20px',
  };



  return (
    <div style={containerStyle}>
      <div style={cardStyle}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={titleStyle}>💾 Database Management</h1>
        </div>

        <div style={statsStyle}>
          <h3 style={{ color: isDark ? '#1e293b' : '#f9fafb', margin: '0 0 10px 0' }}>
            Current Data Summary
          </h3>
          <p style={{ color: isDark ? '#374151' : '#9ca3af', margin: '5px 0' }}>
            📦 Products: {state.products.length}
          </p>
          <p style={{ color: isDark ? '#374151' : '#9ca3af', margin: '5px 0' }}>
            🎨 Canvases: {state.canvases.length}
          </p>
          <p style={{ color: isDark ? '#374151' : '#9ca3af', margin: '5px 0' }}>
            ⚙️ Settings: {state.settings ? 'Configured' : 'Default'}
          </p>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '10px' }}>
          <button
            style={exportButtonStyle}
            onClick={handleExportSQLite}
            disabled={isExporting}
          >
            💾 Export Database (SQLite)
          </button>

          <label style={secondaryButtonStyle}>
            📁 Import Database (SQLite)
            <input
              type="file"
              accept=".db"
              onChange={handleImportSQLite}
              style={{ display: 'none' }}
              disabled={isExporting}
            />
          </label>
        </div>

        <div style={{ marginTop: '24px' }}>
          <h3 style={{ color: isDark ? '#1e293b' : '#ffffff', marginBottom: '12px' }}>
            Database Information
          </h3>
          <ul style={{
            color: isDark ? '#6b7280' : '#9ca3af',
            paddingLeft: '20px',
            margin: 0
          }}>
            <li><strong>SQLite Export:</strong> Complete database backup with all tables and data</li>
            <li><strong>SQLite Import:</strong> Restore database from .db file</li>
            <li><strong>Local Storage:</strong> Data stored locally in SQLite database</li>
            <li><strong>File Naming:</strong> Includes date for easy organization</li>
            <li><strong>Browser Download:</strong> Database file saves to Downloads folder</li>
          </ul>
        </div>

        {isExporting && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
          }}>
            <div style={{
              backgroundColor: isDark ? '#ffffff' : '#1e293b',
              padding: '20px',
              borderRadius: '12px',
              textAlign: 'center',
            }}>
              <h3 style={{ color: isDark ? '#1e293b' : '#ffffff', margin: 0 }}>
                Exporting...
              </h3>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExportScreen;