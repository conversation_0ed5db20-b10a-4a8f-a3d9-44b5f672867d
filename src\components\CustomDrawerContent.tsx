import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';
import ProductsSidebar from './ProductsSidebar';
import { Product } from '../types';

interface CustomDrawerContentProps {
  showProductsSidebar?: boolean;
  onProductSelect?: (product: Product) => void;
  onNavigate?: (screen: string) => void;
}

const CustomDrawerContent: React.FC<CustomDrawerContentProps & any> = ({
  showProductsSidebar = false,
  onProductSelect,
  onNavigate,
  ...props
}) => {
  const { isDark, toggleTheme } = useTheme();
  const { state, logout } = useData();
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to logout?')) {
      logout().catch(() => {
        alert('Error: Failed to logout. Please try again.');
      });
    }
  };

  const handleClearData = () => {
    if (window.confirm('This will permanently delete all your canvases, products, and settings. This action cannot be undone.')) {
      // Implement clear data functionality
      alert('Success: All data has been cleared.');
    }
  };

  const handleProductSelect = (product: Product) => {
    // For now, just log the selection - this could be used for drag and drop
    console.log('Product selected:', product.name);
    setSelectedProducts([product.id]);
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
  };

  const headerStyle: React.CSSProperties = {
    padding: '20px',
    borderBottom: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    marginBottom: '10px',
  };

  const appTitleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: isDark ? '#ffffff' : '#000000',
    margin: '0 0 4px 0',
  };

  const userInfoStyle: React.CSSProperties = {
    fontSize: '14px',
    color: isDark ? '#9ca3af' : '#6b7280',
    margin: 0,
  };

  const navigationSectionStyle: React.CSSProperties = {
    flex: 1,
  };

  const sectionStyle: React.CSSProperties = {
    borderTop: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    padding: '15px',
    marginTop: '10px',
  };

  const sectionTitleStyle: React.CSSProperties = {
    fontSize: '12px',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    color: isDark ? '#9ca3af' : '#6b7280',
    margin: '0 0 10px 0',
  };

  const actionButtonStyle: React.CSSProperties = {
    padding: '12px',
    borderRadius: '8px',
    backgroundColor: isDark ? '#374151' : '#f3f4f6',
    border: 'none',
    cursor: 'pointer',
    marginBottom: '8px',
    textAlign: 'left',
  };

  const actionButtonTextStyle: React.CSSProperties = {
    fontSize: '14px',
    fontWeight: '500',
    color: isDark ? '#ffffff' : '#000000',
    margin: 0,
  };

  const logoutButtonStyle: React.CSSProperties = {
    padding: '15px',
    borderRadius: '8px',
    backgroundColor: '#ef4444',
    border: 'none',
    cursor: 'pointer',
    width: '100%',
  };

  const logoutButtonTextStyle: React.CSSProperties = {
    fontSize: '16px',
    fontWeight: '600',
    color: '#ffffff',
    margin: 0,
    textAlign: 'center',
  };

  const appInfoStyle: React.CSSProperties = {
    fontSize: '12px',
    color: isDark ? '#6b7280' : '#9ca3af',
    textAlign: 'center',
    margin: '0 0 2px 0',
  };

  return (
    <div style={containerStyle}>
      {/* Header */}
      <div style={headerStyle}>
        <h1 style={appTitleStyle}>
          PlomDesign
        </h1>
        {state.user && (
          <p style={userInfoStyle}>
            {state.user.username}
          </p>
        )}
      </div>

      {/* Products Sidebar - Show when requested */}
      {showProductsSidebar && (
        <div style={{ flex: 1, marginBottom: '10px' }}>
          <ProductsSidebar
            onProductSelect={onProductSelect || handleProductSelect}
            selectedProducts={selectedProducts}
          />
        </div>
      )}

      {/* Navigation Items */}
      <div style={navigationSectionStyle}>
        {/* We'll need to render navigation items here */}
        <div style={{ padding: '15px' }}>
          <div
            style={{
              padding: '12px',
              marginBottom: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              backgroundColor: isDark ? '#374151' : '#f3f4f6',
            }}
            onClick={() => onNavigate && onNavigate('Canvas')}
          >
            <span style={{ color: isDark ? '#ffffff' : '#000000' }}>🎨 Canvas</span>
          </div>
          <div
            style={{
              padding: '12px',
              marginBottom: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              backgroundColor: isDark ? '#374151' : '#f3f4f6',
            }}
            onClick={() => onNavigate && onNavigate('Products')}
          >
            <span style={{ color: isDark ? '#ffffff' : '#000000' }}>📦 Products</span>
          </div>
          {/* --- Invoice PDF button --- */}
          <div
            style={{
              padding: '12px',
              marginBottom: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              backgroundColor: isDark ? '#374151' : '#f3f4f6',
            }}
            onClick={() => onNavigate && onNavigate('InvoicePdf')}
          >
            <span style={{ color: isDark ? '#ffffff' : '#000000' }}>🧾 Invoice PDF</span>
          </div>
          <div
            style={{
              padding: '12px',
              marginBottom: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              backgroundColor: isDark ? '#374151' : '#f3f4f6',
            }}
            onClick={() => onNavigate && onNavigate('Settings')}
          >
            <span style={{ color: isDark ? '#ffffff' : '#000000' }}>⚙️ Settings</span>
          </div>
          <div
            style={{
              padding: '12px',
              marginBottom: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              backgroundColor: isDark ? '#374151' : '#f3f4f6',
            }}
            onClick={() => onNavigate && onNavigate('Export')}
          >
            <span style={{ color: isDark ? '#ffffff' : '#000000' }}>💾 Export</span>
          </div>
          <div
            style={{
              padding: '12px',
              marginBottom: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              backgroundColor: isDark ? '#374151' : '#f3f4f6',
            }}
            onClick={() => onNavigate && onNavigate('Help')}
          >
            <span style={{ color: isDark ? '#ffffff' : '#000000' }}>❓ Help & Support</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div style={sectionStyle}>
        <h4 style={sectionTitleStyle}>Quick Actions</h4>

        <button
          style={actionButtonStyle}
          onClick={toggleTheme}
        >
          <span style={actionButtonTextStyle}>
            {isDark ? '☀️ Light Mode' : '🌙 Dark Mode'}
          </span>
        </button>

        <button
          style={{ ...actionButtonStyle, backgroundColor: '#fee2e2' }}
          onClick={handleClearData}
        >
          <span style={{ ...actionButtonTextStyle, color: '#ef4444' }}>
            🗑️ Clear All Data
          </span>
        </button>
      </div>

      {/* User Actions */}
      {state.user && (
        <div style={sectionStyle}>
          <button
            style={logoutButtonStyle}
            onClick={handleLogout}
          >
            <span style={logoutButtonTextStyle}>
              Logout
            </span>
          </button>
        </div>
      )}

      {/* App Info */}
      <div style={sectionStyle}>
        <p style={appInfoStyle}>
          Version 1.0.0
        </p>
        <p style={appInfoStyle}>
          © 2025 PlomDesign
        </p>
      </div>
    </div>
  );
};

export default CustomDrawerContent;