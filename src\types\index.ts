// Core data types for PlomDesign Mobile
export interface Product {
  id: string;
  name: string;
  category: string;
  subcategory: string;
  image?: string;
  description?: string;
  quantity: number;
  price?: number; // Added
  material?: string; // Added
  position: {
    x: number;
    y: number;
  };
  rotation: number;
  createdAt: string;
  updatedAt: string;
}

export interface CanvasProduct extends Product {
  canvasId: string;
}

export interface Canvas {
  id: string;
  name: string;
  products: CanvasProduct[];
  connections: Connection[];
  createdAt: string;
  updatedAt: string;
}

export interface Connection {
  id: string;
  from: string;
  to: string;
  canvasId: string;
}

export interface Settings {
  id: string;
  theme: 'light' | 'dark';
  autoSave: boolean;
  autoSaveInterval: number;
  visibleCategories: Record<string, boolean>;
  removedCategories: string[];
  deletedCategories: string[];
  removedSubcategories: Array<{ category: string; subcategory: string }>;
  deletedSubcategories: Array<{ category: string; subcategory: string }>;
  removedProducts: string[];
  deletedProducts: string[];
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: string;
  username: string;
  email?: string;
  isLoggedIn: boolean;
  licenseInfo?: LicenseInfo;
  createdAt: string;
  updatedAt: string;
}

export interface LicenseInfo {
  id: string;
  licenseKey: string;
  isValid: boolean;
  expiryDate?: string;
  features: string[];
  maxProducts: number;
  createdAt: string;
  updatedAt: string;
}

export interface DragState {
  isDragging: boolean;
  draggedItem?: Product;
  dragOffset: {
    x: number;
    y: number;
  };
}

export interface WindowSizeClass {
  width: 'compact' | 'medium' | 'expanded';
  height: 'compact' | 'medium' | 'expanded';
}