import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type ColorSchemeName = 'light' | 'dark' | null;

interface ThemeContextType {
  theme: ColorSchemeName;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (theme: ColorSchemeName) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

const THEME_STORAGE_KEY = 'plomdesign_theme';

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<ColorSchemeName>(null);

  // Get system theme preference
  const getSystemTheme = (): ColorSchemeName => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  const systemTheme = getSystemTheme();

  // Load saved theme on mount
  useEffect(() => {
    const loadSavedTheme = () => {
      try {
        const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
          setThemeState(savedTheme as ColorSchemeName);
        } else {
          // Use system theme as default
          setThemeState(systemTheme);
        }
      } catch (error) {
        console.error('Failed to load theme:', error);
        setThemeState(systemTheme);
      }
    };

    loadSavedTheme();
  }, [systemTheme]);

  // Apply theme to document body
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const themeToApply = theme || systemTheme || 'light';
      document.body.setAttribute('data-theme', themeToApply);
    }
  }, [theme, systemTheme]);

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      const handleChange = (e: MediaQueryListEvent) => {
        // Only update if user hasn't set a specific theme
        if (theme === null || theme === systemTheme) {
          setThemeState(e.matches ? 'dark' : 'light');
        }
      };

      mediaQuery.addEventListener('change', handleChange);

      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, [theme, systemTheme]);

  const setTheme = (newTheme: ColorSchemeName) => {
    try {
      setThemeState(newTheme);
      if (newTheme) {
        localStorage.setItem(THEME_STORAGE_KEY, newTheme);
      } else {
        localStorage.removeItem(THEME_STORAGE_KEY);
      }
    } catch (error) {
      console.error('Failed to save theme:', error);
    }
  };

  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  };

  // Determine if current theme is dark
  const isDark = theme === 'dark' || (theme === null && systemTheme === 'dark');

  const value: ThemeContextType = {
    theme,
    isDark,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};