import React, { useState, useEffect, useRef } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';
import { Settings } from '../types';

interface SettingsScreenProps {
  onNavigate?: (screen: string) => void;
}

const SettingsScreen: React.FC<SettingsScreenProps> = ({ onNavigate }) => {
  const { isDark, theme, toggleTheme, setTheme } = useTheme();
  const { state, saveSettings, clearAllData } = useData();
  const [settings, setSettings] = useState<Settings | null>(null);
  const [autoSaveInterval, setAutoSaveInterval] = useState('30000');


  useEffect(() => {
    if (state.settings) {
      setSettings(state.settings);
      setAutoSaveInterval(state.settings.autoSaveInterval.toString());
    }
  }, [state.settings]);

  const handleSaveSettings = async (updates: Partial<Settings>) => {
    if (!settings) return;

    try {
      const updatedSettings = { ...settings, ...updates };
      await saveSettings(updatedSettings);
      setSettings(updatedSettings);
    } catch (error) {
      alert('Error: Failed to save settings');
    }
  };

  const handleAutoSaveIntervalChange = async (value: string) => {
    const interval = parseInt(value);
    if (isNaN(interval) || interval < 1000) {
      alert('Error: Auto-save interval must be at least 1000ms');
      return;
    }

    setAutoSaveInterval(value);
    await handleSaveSettings({ autoSaveInterval: interval });
  };

  const handleClearAllData = () => {
    if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      clearAllData().then(() => {
        alert('Success: All data has been cleared');
      }).catch(() => {
        alert('Error: Failed to clear data');
      });
    }
  };

  const containerStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: isDark ? '#0f172a' : '#f9fafb',
    padding: '20px',
  };

  const cardStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
    borderRadius: '12px',
    padding: '24px',
    marginBottom: '20px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    marginBottom: '16px',
    color: isDark ? '#f1f5f9' : '#1e293b',
  };

  const sectionTitleStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: '600',
    marginBottom: '12px',
    color: isDark ? '#f1f5f9' : '#1e293b',
  };

  const settingRowStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px 0',
    borderBottom: `1px solid ${isDark ? '#334155' : '#e5e7eb'}`,
  };

  const settingLabelStyle: React.CSSProperties = {
    fontSize: '16px',
    color: isDark ? '#94a3b8' : '#6b7280',
  };

  const buttonStyle: React.CSSProperties = {
    backgroundColor: '#3b82f6',
    color: '#ffffff',
    border: 'none',
    padding: '10px 20px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '600',
  };

  const dangerButtonStyle: React.CSSProperties = {
    backgroundColor: '#ef4444',
    color: '#ffffff',
    border: 'none',
    padding: '10px 20px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '600',
  };

  const toggleStyle: React.CSSProperties = {
    width: '50px',
    height: '24px',
    borderRadius: '12px',
    backgroundColor: isDark ? '#60a5fa' : '#6b7280',
    position: 'relative',
    cursor: 'pointer',
    transition: 'background-color 0.2s',
  };

  const toggleKnobStyle: React.CSSProperties = {
    width: '20px',
    height: '20px',
    borderRadius: '50%',
    backgroundColor: '#ffffff',
    position: 'absolute',
    top: '2px',
    left: isDark ? '26px' : '2px',
    transition: 'left 0.2s',
  };




  return (
    <div style={containerStyle}>
      {/* Appearance Settings */}
      <div style={cardStyle}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={titleStyle}>⚙️ Settings</h1>
        </div>

        <div>
          <h2 style={sectionTitleStyle}>Appearance</h2>

          <div style={settingRowStyle}>
            <span style={settingLabelStyle}>Dark Mode</span>
            <div
              style={toggleStyle}
              onClick={toggleTheme}
              role="button"
              tabIndex={0}
              onKeyPress={(e) => e.key === 'Enter' && toggleTheme()}
            >
              <div style={toggleKnobStyle}></div>
            </div>
          </div>

        </div>
      </div>

      {/* Auto-save Settings */}
      <div style={cardStyle}>
        <h2 style={sectionTitleStyle}>Auto-save</h2>

        <div style={settingRowStyle}>
          <span style={settingLabelStyle}>Enable Auto-save</span>
          <div
            style={toggleStyle}
            onClick={() => {
              if (settings) {
                saveSettings({
                  ...settings,
                  autoSave: !settings.autoSave,
                });
              }
            }}
            role="button"
            tabIndex={0}
          >
            <div style={{
              ...toggleKnobStyle,
              left: settings?.autoSave ? '26px' : '2px'
            }}></div>
          </div>
        </div>

        {settings?.autoSave && (
          <div style={{ marginTop: '16px' }}>
            <label style={{
              display: 'block',
              marginBottom: '8px',
              color: isDark ? '#94a3b8' : '#6b7280',
              fontSize: '14px'
            }}>
              Auto-save Interval (seconds):
            </label>
            <input
              type="number"
              value={Math.floor(parseInt(autoSaveInterval) / 1000)}
              onChange={(e) => {
                const seconds = parseInt(e.target.value) || 30;
                setAutoSaveInterval((seconds * 1000).toString());
              }}
              onBlur={() => handleAutoSaveIntervalChange(autoSaveInterval)}
              min="1"
              max="300"
              style={{
                width: '100%',
                padding: '8px 12px',
                borderRadius: '6px',
                border: `1px solid ${isDark ? '#475569' : '#d1d5db'}`,
                backgroundColor: isDark ? '#1e293b' : '#ffffff',
                color: isDark ? '#f1f5f9' : '#1e293b',
                fontSize: '14px',
              }}
            />
          </div>
        )}
      </div>

      {/* Data Management */}
      <div style={cardStyle}>
        <h2 style={sectionTitleStyle}>Data Management</h2>

        <div style={{ marginBottom: '16px' }}>
          <p style={{
            color: isDark ? '#94a3b8' : '#6b7280',
            fontSize: '14px',
            marginBottom: '16px'
          }}>
            Current data: {state.products.length} products, {state.canvases.length} canvases
          </p>
        </div>

        <button
          style={dangerButtonStyle}
          onClick={handleClearAllData}
        >
          🗑️ Clear All Data
        </button>
      </div>

      {/* App Info */}
      <div style={cardStyle}>
        <h2 style={sectionTitleStyle}>About</h2>
        <p style={{
          color: isDark ? '#94a3b8' : '#6b7280',
          fontSize: '14px',
          margin: 0
        }}>
          PlomDesignMobile v1.0.0
          <br />
          A modern product design and canvas management application
        </p>
      </div>
    </div>
  );
};

export default SettingsScreen;