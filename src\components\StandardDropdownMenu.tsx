import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface MenuItem {
  id: string;
  label: string;
  action?: () => void;
  disabled?: boolean;
}

interface MenuSection {
  id: string;
  label: string;
  items: MenuItem[];
}

interface StandardDropdownMenuProps {
  onNavigate?: (screen: string) => void;
}

const StandardDropdownMenu: React.FC<StandardDropdownMenuProps> = ({ onNavigate }) => {
  const { isDark } = useTheme();
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenDropdown(null);
      }
    };

    if (openDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdown]);

  const toggleDropdown = (menuId: string) => {
    setOpenDropdown(openDropdown === menuId ? null : menuId);
  };

  const handleMenuItemClick = (item: MenuItem) => {
    if (item.action) {
      item.action();
    } else if (onNavigate && item.id.startsWith('screen-')) {
      const screenName = item.id.replace('screen-', '');
      onNavigate(screenName);
    }
    setOpenDropdown(null); // Close dropdown after action
  };

  const menuSections: MenuSection[] = [
    {
      id: 'file',
      label: 'File',
      items: [
        { id: 'screen-products', label: 'Products' },
        { id: 'screen-export', label: 'Export' },
        { id: 'screen-settings', label: 'Settings' },
      ]
    },
    {
      id: 'edit',
      label: 'Edit',
      items: [
        { id: 'undo', label: 'Undo', disabled: true },
        { id: 'redo', label: 'Redo', disabled: true },
        { id: 'clear-canvas', label: 'Clear Canvas' },
      ]
    },
    {
      id: 'view',
      label: 'View',
      items: [
        { id: 'zoom-in', label: 'Zoom In' },
        { id: 'zoom-out', label: 'Zoom Out' },
        { id: 'fit-canvas', label: 'Fit to Canvas' },
      ]
    },
    {
      id: 'help',
      label: 'Help',
      items: [
        { id: 'screen-help', label: 'Help' },
        { id: 'about', label: 'About' },
      ]
    }
  ];

  const menuBarStyle: React.CSSProperties = {
    position: 'fixed',
    top: '10px',
    left: '10px',
    display: 'flex',
    gap: '2px',
    zIndex: 1000,
  };

  const menuButtonStyle = (isActive: boolean): React.CSSProperties => ({
    backgroundColor: isActive ? (isDark ? '#334155' : '#e5e7eb') : (isDark ? '#1e293b' : '#ffffff'),
    color: isDark ? '#f1f5f9' : '#1e293b',
    border: `1px solid ${isDark ? '#475569' : '#d1d5db'}`,
    borderRadius: '4px',
    padding: '6px 12px',
    cursor: 'pointer',
    fontSize: '13px',
    fontWeight: '500',
    position: 'relative',
    transition: 'all 0.15s ease',
  });

  const dropdownStyle: React.CSSProperties = {
    position: 'absolute',
    top: '100%',
    left: '0',
    backgroundColor: isDark ? '#0f172a' : '#ffffff',
    border: `1px solid ${isDark ? '#475569' : '#d1d5db'}`,
    borderRadius: '4px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    minWidth: '160px',
    zIndex: 1001,
    padding: '4px 0',
  };

  const menuItemStyle = (disabled: boolean): React.CSSProperties => ({
    backgroundColor: 'transparent',
    color: disabled ? (isDark ? '#64748b' : '#9ca3af') : (isDark ? '#f1f5f9' : '#1e293b'),
    border: 'none',
    padding: '8px 16px',
    cursor: disabled ? 'not-allowed' : 'pointer',
    fontSize: '13px',
    fontWeight: '400',
    textAlign: 'left',
    width: '100%',
    transition: 'all 0.15s ease',
  });

  return (
    <div ref={menuRef} style={menuBarStyle}>
      {menuSections.map((section) => (
        <div key={section.id} style={{ position: 'relative' }}>
          <button
            onClick={() => toggleDropdown(section.id)}
            style={menuButtonStyle(openDropdown === section.id)}
            onMouseEnter={(e) => {
              if (openDropdown !== section.id) {
                e.currentTarget.style.backgroundColor = isDark ? '#334155' : '#f8fafc';
              }
            }}
            onMouseLeave={(e) => {
              if (openDropdown !== section.id) {
                e.currentTarget.style.backgroundColor = isDark ? '#1e293b' : '#ffffff';
              }
            }}
          >
            {section.label}
          </button>

          {openDropdown === section.id && (
            <div style={dropdownStyle}>
              {section.items.map((item) => (
                <button
                  key={item.id}
                  onClick={() => !item.disabled && handleMenuItemClick(item)}
                  style={menuItemStyle(item.disabled || false)}
                  onMouseEnter={(e) => {
                    if (!item.disabled) {
                      e.currentTarget.style.backgroundColor = isDark ? '#334155' : '#f1f5f9';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!item.disabled) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                  disabled={item.disabled}
                >
                  {item.label}
                </button>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default StandardDropdownMenu;