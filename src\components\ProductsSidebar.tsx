import React, { useState, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';
import { Product } from '../types';

interface ProductsSidebarProps {
  onProductSelect?: (product: Product) => void;
  selectedProducts?: string[];
}

const ProductsSidebar: React.FC<ProductsSidebarProps> = ({
  onProductSelect,
  selectedProducts = [],
}) => {
  const { isDark } = useTheme();
  const { state } = useData();
  const [searchQuery, setSearchQuery] = useState('');

  // Filter products based on search query
  const filteredProducts = useMemo(() => {
    if (!searchQuery.trim()) {
      return state.products;
    }

    const query = searchQuery.toLowerCase();
    return state.products.filter(product =>
      product.name.toLowerCase().includes(query) ||
      product.category.toLowerCase().includes(query) ||
      product.subcategory.toLowerCase().includes(query) ||
      (product.description && product.description.toLowerCase().includes(query))
    );
  }, [state.products, searchQuery]);

  const handleProductPress = (product: Product) => {
    if (onProductSelect) {
      onProductSelect(product);
    }
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: isDark ? '#0f172a' : '#ffffff',
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px 16px',
    borderBottom: `1px solid ${isDark ? '#334155' : '#e5e7eb'}`,
  };

  const headerTitleStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: 'bold',
    color: isDark ? '#f1f5f9' : '#1e293b',
    margin: 0,
  };

  const productCountStyle: React.CSSProperties = {
    fontSize: '12px',
    color: isDark ? '#94a3b8' : '#6b7280',
  };

  const searchContainerStyle: React.CSSProperties = {
    padding: '8px 16px',
  };

  const searchInputStyle: React.CSSProperties = {
    width: '100%',
    height: '40px',
    padding: '0 12px',
    borderRadius: '8px',
    border: `1px solid ${isDark ? '#475569' : '#d1d5db'}`,
    backgroundColor: isDark ? '#1e293b' : '#f9fafb',
    color: isDark ? '#f1f5f9' : '#1e293b',
    fontSize: '14px',
    outline: 'none',
  };

  const productsListStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'auto',
    padding: '0 16px',
  };

  const getProductItemStyle = (isSelected: boolean): React.CSSProperties => ({
    display: 'flex',
    alignItems: 'center',
    padding: '12px',
    margin: '4px 0',
    borderRadius: '8px',
    border: `1px solid ${isSelected ? '#60a5fa' : (isDark ? '#475569' : '#e5e7eb')}`,
    backgroundColor: isSelected
      ? (isDark ? '#334155' : '#e5e7eb')
      : (isDark ? '#1e293b' : '#ffffff'),
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
  });

  const productImageStyle: React.CSSProperties = {
    width: '40px',
    height: '40px',
    borderRadius: '6px',
    backgroundColor: isDark ? '#334155' : '#f3f4f6',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: '12px',
    flexShrink: 0,
  };

  const productImageTextStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: 'bold',
    color: isDark ? '#94a3b8' : '#6b7280',
  };

  const productInfoStyle: React.CSSProperties = {
    flex: 1,
    minWidth: 0,
  };

  const productNameStyle: React.CSSProperties = {
    fontSize: '14px',
    fontWeight: '600',
    color: isDark ? '#f1f5f9' : '#1e293b',
    margin: '0 0 2px 0',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  };

  const productCategoryStyle: React.CSSProperties = {
    fontSize: '12px',
    color: isDark ? '#94a3b8' : '#6b7280',
    margin: '0 0 2px 0',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  };

  const productQuantityStyle: React.CSSProperties = {
    fontSize: '11px',
    fontWeight: '500',
    color: isDark ? '#60a5fa' : '#3b82f6',
    margin: 0,
  };

  const dragHandleStyle: React.CSSProperties = {
    padding: '6px',
    color: isDark ? '#cbd5e1' : '#374151',
    fontSize: '14px',
    fontWeight: 'bold',
    userSelect: 'none',
    cursor: 'grab',
  };

  const emptyStateStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '40px 20px',
    textAlign: 'center',
  };

  const emptyStateTextStyle: React.CSSProperties = {
    fontSize: '16px',
    fontWeight: '500',
    color: isDark ? '#94a3b8' : '#6b7280',
    margin: '0 0 8px 0',
  };

  const emptyStateSubtextStyle: React.CSSProperties = {
    fontSize: '14px',
    color: isDark ? '#94a3b8' : '#9ca3af',
    margin: 0,
  };

  const instructionsStyle: React.CSSProperties = {
    padding: '12px 16px',
    borderTop: `1px solid ${isDark ? '#334155' : '#e5e7eb'}`,
    textAlign: 'center',
  };

  const instructionsTextStyle: React.CSSProperties = {
    fontSize: '12px',
    color: isDark ? '#94a3b8' : '#6b7280',
    margin: 0,
  };

  const handleDragStart = (e: React.DragEvent, product: Product) => {
    // Set the drag data
    e.dataTransfer.setData('application/json', JSON.stringify(product));
    e.dataTransfer.effectAllowed = 'copy';

    console.log('Started dragging product:', product.name);
  };

  const renderProductItem = (product: Product) => {
    const isSelected = selectedProducts.includes(product.id);

    return (
      <div
        key={product.id}
        style={getProductItemStyle(isSelected)}
        onClick={() => handleProductPress(product)}
        draggable={true}
        onDragStart={(e) => handleDragStart(e, product)}
        onDragEnd={() => {
          console.log('Finished dragging product:', product.name);
        }}
      >
        {/* Product Image Placeholder */}
        <div style={productImageStyle}>
          <span style={productImageTextStyle}>
            {product.name.charAt(0).toUpperCase()}
          </span>
        </div>

        {/* Product Info */}
        <div style={productInfoStyle}>
          <h4 style={productNameStyle}>
            {product.name}
          </h4>
          <p style={productCategoryStyle}>
            {product.category} • {product.subcategory}
          </p>
          <p style={productQuantityStyle}>
            Qty: {product.quantity}
          </p>
        </div>

        {/* Drag Handle */}
        <div style={dragHandleStyle}>
          ⋮⋮
        </div>
      </div>
    );
  };

  return (
    <div style={containerStyle}>
      {/* Header */}
      <div style={headerStyle}>
        <h3 style={headerTitleStyle}>Products</h3>
        <span style={productCountStyle}>
          {filteredProducts.length} of {state.products.length}
        </span>
      </div>

      {/* Search Input */}
      <div style={searchContainerStyle}>
        <input
          type="text"
          style={searchInputStyle}
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Products List */}
      <div style={productsListStyle}>
        {filteredProducts.length === 0 ? (
          <div style={emptyStateStyle}>
            <p style={emptyStateTextStyle}>
              {searchQuery.trim() ? 'No products found' : 'No products available'}
            </p>
            <p style={emptyStateSubtextStyle}>
              {searchQuery.trim() ? 'Try a different search term' : 'Add products in the Products tab'}
            </p>
          </div>
        ) : (
          filteredProducts.map(renderProductItem)
        )}
      </div>

      {/* Instructions */}
      <div style={instructionsStyle}>
        <p style={instructionsTextStyle}>
          💡 Click and drag products to canvas
        </p>
      </div>
    </div>
  );
};

export default ProductsSidebar;