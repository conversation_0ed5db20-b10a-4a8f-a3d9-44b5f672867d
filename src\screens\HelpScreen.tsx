import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface HelpScreenProps {
  onNavigate?: (screen: string) => void;
}

const HelpScreen: React.FC<HelpScreenProps> = ({ onNavigate }) => {
  const { isDark } = useTheme();
  const [expandedSection, setExpandedSection] = useState<string | null>(null);


  const toggleSection = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  const handleContactSupport = () => {
    window.open('mailto:<EMAIL>?subject=PlomDesign Web Support', '_blank');
  };

  const handleOpenDocumentation = () => {
    window.open('https://plomdesign.com/docs', '_blank');
  };

  const containerStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: isDark ? '#1e293b' : '#f9fafb',
    padding: '20px',
  };

  const cardStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#ffffff' : '#1e293b',
    borderRadius: '12px',
    padding: '24px',
    marginBottom: '20px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    marginBottom: '16px',
    color: isDark ? '#1e293b' : '#ffffff',
  };

  const sectionStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#f9fafb' : '#374151',
    borderRadius: '8px',
    marginBottom: '12px',
    overflow: 'hidden',
  };

  const sectionHeaderStyle: React.CSSProperties = {
    padding: '16px',
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    userSelect: 'none',
  };

  const sectionTitleStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: '600',
    color: isDark ? '#1e293b' : '#f9fafb',
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  };

  const expandIconStyle: React.CSSProperties = {
    fontSize: '20px',
    color: isDark ? '#6b7280' : '#9ca3af',
    transition: 'transform 0.2s',
  };

  const sectionContentStyle: React.CSSProperties = {
    padding: '16px',
    borderTop: `1px solid ${isDark ? '#e5e7eb' : '#4b5563'}`,
  };

  const textStyle: React.CSSProperties = {
    color: isDark ? '#374151' : '#d1d5db',
    fontSize: '16px',
    lineHeight: '1.6',
    marginBottom: '12px',
  };

  const listStyle: React.CSSProperties = {
    color: isDark ? '#374151' : '#d1d5db',
    paddingLeft: '20px',
  };

  const boldTextStyle: React.CSSProperties = {
    fontWeight: '600',
    color: isDark ? '#1e293b' : '#ffffff',
  };

  const supportButtonStyle: React.CSSProperties = {
    backgroundColor: '#3b82f6',
    color: '#ffffff',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '12px',
    width: '100%',
    maxWidth: '300px',
  };

  const secondaryButtonStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#4b5563' : '#f3f4f6',
    color: isDark ? '#ffffff' : '#000000',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: '600',
    width: '100%',
    maxWidth: '300px',
  };



  const renderHelpSection = (
    id: string,
    title: string,
    icon: string,
    content: React.ReactNode
  ) => (
    <div style={sectionStyle}>
      <div
        style={sectionHeaderStyle}
        onClick={() => toggleSection(id)}
      >
        <div style={sectionTitleStyle}>
          <span style={{ fontSize: '20px' }}>{icon}</span>
          <span>{title}</span>
        </div>
        <span style={expandIconStyle}>
          {expandedSection === id ? '−' : '+'}
        </span>
      </div>

      {expandedSection === id && (
        <div style={sectionContentStyle}>
          {content}
        </div>
      )}
    </div>
  );

  return (
    <div style={containerStyle}>
      <div style={cardStyle}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={titleStyle}>❓ Help & Support</h1>
        </div>

        <p style={{
          color: isDark ? '#6b7280' : '#9ca3af',
          fontSize: '16px',
          marginBottom: '20px'
        }}>
          Get help with using PlomDesign Web
        </p>

        {/* Getting Started */}
        {renderHelpSection(
          'getting-started',
          'Getting Started',
          '🚀',
          <div>
            <p style={textStyle}>
              Welcome to PlomDesign Web! Here's how to get started:
            </p>
            <ul style={listStyle}>
              <li><strong style={boldTextStyle}>Create Products:</strong> Go to the Products screen to add items to your library</li>
              <li><strong style={boldTextStyle}>Design Canvases:</strong> Use the Canvas screen to drag and drop products</li>
              <li><strong style={boldTextStyle}>Customize Settings:</strong> Adjust themes, auto-save, and other preferences</li>
              <li><strong style={boldTextStyle}>Export Your Work:</strong> Save your designs in various formats</li>
            </ul>
          </div>
        )}

        {/* Canvas Features */}
        {renderHelpSection(
          'canvas-features',
          'Canvas Features',
          '🎨',
          <div>
            <p style={textStyle}>
              Learn how to use the canvas effectively:
            </p>
            <ul style={listStyle}>
              <li><strong style={boldTextStyle}>Drag & Drop:</strong> Click and drag products from the sidebar to the canvas</li>
              <li><strong style={boldTextStyle}>Move Products:</strong> Drag existing products to reposition them</li>
              <li><strong style={boldTextStyle}>Grid System:</strong> Enable grid in settings for precise placement</li>
              <li><strong style={boldTextStyle}>Auto-save:</strong> Your work is automatically saved as you design</li>
            </ul>
          </div>
        )}

        {/* Product Management */}
        {renderHelpSection(
          'product-management',
          'Product Management',
          '📦',
          <div>
            <p style={textStyle}>
              Manage your product library:
            </p>
            <ul style={listStyle}>
              <li><strong style={boldTextStyle}>Add Products:</strong> Use the "Add Product" button to create new items</li>
              <li><strong style={boldTextStyle}>Edit Products:</strong> Click the Edit button on any product card</li>
              <li><strong style={boldTextStyle}>Delete Products:</strong> Use the Delete button with confirmation</li>
              <li><strong style={boldTextStyle}>Categories:</strong> Organize products by category and subcategory</li>
            </ul>
          </div>
        )}

        {/* Export & Backup */}
        {renderHelpSection(
          'export-backup',
          'Export & Backup',
          '💾',
          <div>
            <p style={textStyle}>
              Export your work in various formats:
            </p>
            <ul style={listStyle}>
              <li><strong style={boldTextStyle}>JSON Export:</strong> Complete data export for backup or transfer</li>
              <li><strong style={boldTextStyle}>CSV Export:</strong> Spreadsheet-compatible product data</li>
              <li><strong style={boldTextStyle}>Full Backup:</strong> Export all app data including settings</li>
              <li><strong style={boldTextStyle}>Auto-save:</strong> Never lose your work with automatic saving</li>
            </ul>
          </div>
        )}

        {/* Troubleshooting */}
        {renderHelpSection(
          'troubleshooting',
          'Troubleshooting',
          '🔧',
          <div>
            <p style={textStyle}>
              Common issues and solutions:
            </p>
            <ul style={listStyle}>
              <li><strong style={boldTextStyle}>App Not Loading:</strong> Clear browser cache and reload</li>
              <li><strong style={boldTextStyle}>Data Not Saving:</strong> Check auto-save settings and browser storage</li>
              <li><strong style={boldTextStyle}>Canvas Issues:</strong> Try refreshing the page or clearing canvas</li>
              <li><strong style={boldTextStyle}>Export Problems:</strong> Check browser download settings</li>
            </ul>
          </div>
        )}

        {/* About */}
        {renderHelpSection(
          'about',
          'About PlomDesign',
          'ℹ️',
          <div>
            <p style={textStyle}>
              PlomDesign Web is a powerful design tool for creating and managing product canvases.
            </p>
            <div style={{ marginTop: '16px' }}>
              <p style={{ color: isDark ? '#6b7280' : '#9ca3af', margin: '4px 0' }}>
                <strong>Version:</strong> 1.0.0
              </p>
              <p style={{ color: isDark ? '#6b7280' : '#9ca3af', margin: '4px 0' }}>
                <strong>Platform:</strong> Web Application
              </p>
              <p style={{ color: isDark ? '#6b7280' : '#9ca3af', margin: '4px 0' }}>
                <strong>Storage:</strong> Browser Local Storage
              </p>
              <p style={{ color: isDark ? '#6b7280' : '#9ca3af', margin: '4px 0' }}>
                <strong>Features:</strong> Drag & Drop, Product Management, Export
              </p>
            </div>
          </div>
        )}

        {/* Support Actions */}
        <div style={{
          marginTop: '24px',
          padding: '20px',
          backgroundColor: isDark ? '#f9fafb' : '#374151',
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <h3 style={{
            color: isDark ? '#1e293b' : '#f9fafb',
            margin: '0 0 16px 0'
          }}>
            Need More Help?
          </h3>

          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '10px' }}>
            <button
              style={supportButtonStyle}
              onClick={handleContactSupport}
            >
              📧 Contact Support
            </button>

            <button
              style={secondaryButtonStyle}
              onClick={handleOpenDocumentation}
            >
              📚 View Documentation
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpScreen;