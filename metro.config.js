const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resolver: {
    alias: {
      'react-native': 'react-native-web',
    },
    platforms: ['ios', 'android', 'web'],
    // Handle missing React DevTools module
    resolveRequest: (context, moduleName, platform) => {
      // Handle the problematic React DevTools module
      if (moduleName.includes('ReactDevToolsSettingsManager')) {
        return {
          type: 'empty',
        };
      }
      return context.resolveRequest(context, moduleName, platform);
    },
  },
  watchFolders: [
    path.resolve(__dirname, './src'),
  ],
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  server: {
    port: 8082,
  },
};

// Web-specific configuration
if (process.env.PLATFORM === 'web') {
  const originalResolveRequest = config.resolver.resolveRequest;
  config.resolver.resolveRequest = (context, moduleName, platform) => {
    // Handle the problematic React DevTools module
    if (moduleName.includes('ReactDevToolsSettingsManager')) {
      return {
        type: 'empty',
      };
    }

    // Handle web-specific index file
    if (moduleName === './index') {
      return {
        filePath: path.resolve(__dirname, 'index.web.js'),
        type: 'sourceFile',
      };
    }

    // Use original resolver for other modules
    return originalResolveRequest(context, moduleName, platform);
  };
} else {
  // For non-web platforms, keep the React DevTools error handling
  const originalResolveRequest = config.resolver.resolveRequest;
  config.resolver.resolveRequest = (context, moduleName, platform) => {
    // Handle the problematic React DevTools module
    if (moduleName.includes('ReactDevToolsSettingsManager')) {
      return {
        type: 'empty',
      };
    }

    // Use original resolver for other modules
    return originalResolveRequest(context, moduleName, platform);
  };
}

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
