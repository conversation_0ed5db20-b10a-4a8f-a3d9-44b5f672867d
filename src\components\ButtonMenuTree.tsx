import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface MenuItem {
  id: string;
  label: string;
  icon?: string;
  children?: MenuItem[];
  action?: () => void;
}

interface ButtonMenuTreeProps {
  menuItems: MenuItem[];
  onNavigate?: (screen: string) => void;
}

const ButtonMenuTree: React.FC<ButtonMenuTreeProps> = ({ menuItems, onNavigate }) => {
  const { isDark } = useTheme();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setExpandedItems(new Set()); // Close all expanded items
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
    if (!isDropdownOpen) {
      setExpandedItems(new Set()); // Reset expanded items when opening
    }
  };

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.children && item.children.length > 0) {
      toggleExpanded(item.id);
    } else if (item.action) {
      item.action();
      setIsDropdownOpen(false); // Close dropdown after action
    } else if (onNavigate && item.id.startsWith('screen-')) {
      const screenName = item.id.replace('screen-', '');
      onNavigate(screenName);
      setIsDropdownOpen(false); // Close dropdown after navigation
    }
  };

  const renderMenuItem = (item: MenuItem, level: number = 0): React.ReactNode => {
    const isExpanded = expandedItems.has(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id} style={{ marginLeft: level * 16 }}>
        <button
          onClick={() => handleItemClick(item)}
          style={{
            backgroundColor: 'transparent',
            color: isDark ? '#f1f5f9' : '#1e293b',
            border: 'none',
            borderRadius: '4px',
            padding: '8px 12px',
            margin: '1px 0',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            width: '100%',
            textAlign: 'left',
            transition: 'all 0.2s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = isDark ? '#334155' : '#f1f5f9';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          {item.icon && <span>{item.icon}</span>}
          <span style={{ flex: 1 }}>{item.label}</span>
          {hasChildren && (
            <span style={{
              fontSize: '12px',
              transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease'
            }}>
              ▶
            </span>
          )}
        </button>

        {hasChildren && isExpanded && (
          <div style={{ marginTop: '2px' }}>
            {item.children!.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const triggerButtonStyle: React.CSSProperties = {
    position: 'fixed',
    top: '10px',
    right: '10px',
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
    color: isDark ? '#f1f5f9' : '#1e293b',
    border: `1px solid ${isDark ? '#475569' : '#d1d5db'}`,
    borderRadius: '6px',
    padding: '8px 12px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '600',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    zIndex: 1000,
    transition: 'all 0.2s ease',
  };

  const dropdownStyle: React.CSSProperties = {
    position: 'fixed',
    top: '50px', // Position below the trigger button
    right: '10px',
    backgroundColor: isDark ? '#0f172a' : '#ffffff',
    border: `1px solid ${isDark ? '#475569' : '#d1d5db'}`,
    borderRadius: '8px',
    padding: '8px 0',
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.15)',
    zIndex: 1001,
    minWidth: '200px',
    maxWidth: '280px',
    maxHeight: 'calc(100vh - 80px)',
    overflowY: 'auto',
    opacity: isDropdownOpen ? 1 : 0,
    transform: isDropdownOpen ? 'translateY(0)' : 'translateY(-10px)',
    pointerEvents: isDropdownOpen ? 'auto' : 'none',
    transition: 'all 0.2s ease',
  };

  return (
    <div ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        onClick={toggleDropdown}
        style={triggerButtonStyle}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = isDark ? '#334155' : '#f8fafc';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = isDark ? '#1e293b' : '#ffffff';
        }}
      >
        <span>☰</span>
        <span>Menu</span>
        <span style={{
          fontSize: '12px',
          transform: isDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.2s ease'
        }}>
          ▼
        </span>
      </button>

      {/* Dropdown Menu */}
      <div style={dropdownStyle}>
        {menuItems.map(item => renderMenuItem(item))}
      </div>
    </div>
  );
};

export default ButtonMenuTree;