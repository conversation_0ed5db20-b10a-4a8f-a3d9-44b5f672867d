/**
 * Web-specific entry point for React Native app
 * @format
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './src/App';

// Web-specific rendering - bypass React Native's AppRegistry for web
if (typeof document !== 'undefined') {
  const rootElement = document.getElementById('root');
  if (rootElement) {
    const root = ReactDOM.createRoot(rootElement);
    root.render(<App />);
  }
}